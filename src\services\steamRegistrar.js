const logger = require('../utils/logger');
const config = require('../config/env');
const { steamSelectors, steamConfig } = require('../config/steam-selectors');
const Humanizer = require('../utils/humanizer');
const CaptchaSolver = require('./captchaSolver');

class SteamRegistrar {
    constructor(stealthBrowser, proxyManager) {
        this.browser = stealthBrowser;
        this.proxyManager = proxyManager;
        this.humanizer = new Humanizer();
        this.captchaSolver = new CaptchaSolver();
    }

    /**
     * Register a Steam account
     */
    async registerAccount(email, password, country = null) {
        let proxy = null;
        let page = null;
        
        try {
            logger.accountStart(email);
            
            // Get proxy for this account
            proxy = await this.proxyManager.getProxyForAccount(email);
            logger.proxyAssigned(email, proxy);
            
            // Create browser context with proxy
            await this.browser.createContext(email, proxy);
            
            // Create page
            page = await this.browser.createPage(email);
            
            // Navigate to Steam registration page
            await this.navigateToRegistration(page, email);
            
            // Fill registration form
            await this.fillRegistrationForm(page, email, password, country);
            
            // Handle captcha if present
            await this.handleCaptcha(page, email, proxy);
            
            // Submit form
            const result = await this.submitForm(page, email);
            
            logger.accountSuccess(email, result);
            return result;
            
        } catch (error) {
            logger.accountFailure(email, error, { proxy });
            
            // Try with different proxy if proxy-related error
            if (this.isProxyError(error)) {
                try {
                    if (proxy) {
                        // Mark current proxy as failed and try to get a new one
                        const newProxy = await this.proxyManager.markProxyFailed(email, proxy, error);
                        logger.info('Retrying with new proxy', { email, newProxy: newProxy ? `${newProxy.proxy_address}:${newProxy.port}` : 'none' });
                    } else {
                        logger.info('Proxy error occurred but no proxy was assigned - retrying without proxy', { email });
                    }

                    // If we still can't get a working proxy, try without proxy
                    if (!proxy || !await this.proxyManager.getProxyForAccount(email)) {
                        logger.info('No working proxy available - attempting without proxy', { email });
                        // Force no proxy for this retry
                        this.proxyManager.releaseProxy(email);
                    }

                    return await this.registerAccount(email, password, country);
                } catch (retryError) {
                    // If retry also fails, try one more time without any proxy
                    if (proxy) {
                        logger.info('Final attempt without proxy', { email });
                        this.proxyManager.releaseProxy(email);
                        try {
                            return await this.registerAccount(email, password, country);
                        } catch (finalError) {
                            // As a last resort, try BrightData Web Unlocker
                            logger.info('Attempting BrightData Web Unlocker as final fallback', { email });
                            try {
                                return await this.tryBrightDataFallback(email, password, country);
                            } catch (brightDataError) {
                                logger.error('BrightData fallback also failed', {
                                    email,
                                    error: brightDataError.message
                                });
                                throw finalError; // Throw the original error
                            }
                        }
                    }
                    throw retryError;
                }
            }
            
            throw error;
        } finally {
            // Clean up
            if (page) {
                try {
                    await page.close();
                } catch (e) {
                    logger.warn('Error closing page', { email, error: e.message });
                }
            }
        }
    }

    /**
     * Navigate to Steam registration page
     */
    async navigateToRegistration(page, email) {
        logger.browserAction(email, 'navigate', { url: config.steam.registrationUrl });

        try {
            await this.humanizer.humanNavigate(page, config.steam.registrationUrl, {
                waitForLoad: true,
                readingTime: true
            });

            // Wait for form to be ready
            await this.humanizer.waitForInteractable(page, steamSelectors.emailInput, steamConfig.timeouts.pageLoad);

            // Simulate reading the page
            await this.humanizer.simulateReading(page, 1);

        } catch (error) {
            // If navigation fails, it might be a proxy issue
            logger.warn('Navigation failed', { email, error: error.message });
            throw error;
        }
    }

    /**
     * Fill the Steam registration form
     */
    async fillRegistrationForm(page, email, password, country) {
        logger.browserAction(email, 'fill_form');
        
        // Fill email field
        await this.fillEmailField(page, email);
        
        // Fill confirm email field
        await this.fillConfirmEmailField(page, email);
        
        // Select country
        await this.selectCountry(page, email, country);
        
        // Check age confirmation
        await this.confirmAge(page, email);
        
        // Add realistic pause before proceeding
        await this.humanizer.humanPause('thinking');
    }

    /**
     * Fill email field with human-like behavior
     */
    async fillEmailField(page, email) {
        const selectors = [steamSelectors.emailInput, steamSelectors.emailInputAlt];
        
        for (const selector of selectors) {
            try {
                await this.humanizer.waitForInteractable(page, selector, 5000);
                await this.humanizer.fillFormField(page, selector, email, {
                    errorProbability: 0.01, // Very low error rate for email
                    minDelay: 80,
                    maxDelay: 180
                });
                logger.browserAction(email, 'email_filled');
                return;
            } catch (error) {
                continue;
            }
        }
        
        throw new Error('Could not find email input field');
    }

    /**
     * Fill confirm email field
     */
    async fillConfirmEmailField(page, email) {
        const selectors = [steamSelectors.confirmEmailInput, steamSelectors.confirmEmailInputAlt];
        
        for (const selector of selectors) {
            try {
                await this.humanizer.waitForInteractable(page, selector, 5000);
                await this.humanizer.fillFormField(page, selector, email, {
                    errorProbability: 0.005, // Even lower error rate for confirmation
                    minDelay: 70,
                    maxDelay: 160
                });
                logger.browserAction(email, 'confirm_email_filled');
                return;
            } catch (error) {
                continue;
            }
        }
        
        throw new Error('Could not find confirm email input field');
    }

    /**
     * Select country from dropdown
     */
    async selectCountry(page, email, country) {
        // Use provided country or select random one
        const selectedCountry = country || this.getRandomCountry();
        
        const selectors = [
            steamSelectors.countrySelect, 
            steamSelectors.countrySelectAlt,
            'select[name="country"]',
            'select',
            'combobox'
        ];
        
        for (const selector of selectors) {
            try {
                await this.humanizer.waitForInteractable(page, selector, 5000);
                
                // Set country via JavaScript to avoid dropdown interaction issues
                await page.evaluate(({ selector, country }) => {
                    const selectElement = document.querySelector(selector);
                    if (selectElement) {
                        // Find option by text content
                        const options = Array.from(selectElement.options);
                        const targetOption = options.find(option => 
                            option.text.includes(country) || 
                            option.value.includes(country) ||
                            option.text === country
                        );
                        
                        if (targetOption) {
                            selectElement.value = targetOption.value;
                            // Trigger change event
                            selectElement.dispatchEvent(new Event('change', { bubbles: true }));
                            selectElement.dispatchEvent(new Event('input', { bubbles: true }));
                            return true;
                        }
                        
                        // Fallback: set to first US/Canada option
                        const fallbackOption = options.find(option => 
                            option.text.includes('United States') || 
                            option.text.includes('Canada')
                        );
                        if (fallbackOption) {
                            selectElement.value = fallbackOption.value;
                            selectElement.dispatchEvent(new Event('change', { bubbles: true }));
                            selectElement.dispatchEvent(new Event('input', { bubbles: true }));
                            return true;
                        }
                    }
                    return false;
                }, { selector, country: selectedCountry });
                
                await this.humanizer.delay(this.humanizer.randomDelay(500, 1000));
                logger.browserAction(email, 'country_selected', { country: selectedCountry });
                return;
                
            } catch (error) {
                logger.debug('Country selector failed', { selector, error: error.message });
                continue;
            }
        }
        
        throw new Error('Could not find country select field');
    }

    /**
     * Confirm age checkbox
     */
    async confirmAge(page, email) {
        const selectors = [
            steamSelectors.ageCheckbox, 
            steamSelectors.ageCheckboxAlt,
            'input[type="checkbox"]',
            '[type="checkbox"]',
            'checkbox'
        ];
        
        for (const selector of selectors) {
            try {
                await this.humanizer.waitForInteractable(page, selector, 5000);
                await this.humanizer.handleCheckbox(page, selector, true);
                logger.browserAction(email, 'age_confirmed');
                return;
            } catch (error) {
                logger.debug('Age checkbox selector failed', { selector, error: error.message });
                continue;
            }
        }
        
        throw new Error('Could not find age confirmation checkbox');
    }

    /**
     * Handle captcha if present
     */
    async handleCaptcha(page, email, proxy) {
        try {
            // Wait a moment for captcha to potentially appear
            await this.humanizer.delay(2000);

            const captchaResult = await this.captchaSolver.autoSolveCaptcha(email, page, proxy);

            if (captchaResult) {
                logger.browserAction(email, 'captcha_solved', {
                    solver: captchaResult.solver,
                    duration: captchaResult.duration
                });

                // Wait longer for captcha solution to be processed and page to stabilize
                // This is especially important for BrightData solutions
                if (captchaResult.solver === 'brightdata') {
                    logger.debug('Waiting for BrightData CAPTCHA solution to stabilize', { email });
                    await this.humanizer.delay(3000);
                } else {
                    await this.humanizer.delay(1500);
                }

                // Verify the page is ready for form submission
                await this.verifyPageReadyForSubmission(page, email);
            }

        } catch (error) {
            logger.warn('Captcha handling failed', { email, error: error.message });
            // Don't throw here - captcha might not be required
        }
    }

    /**
     * Verify the page is ready for form submission after CAPTCHA solving
     */
    async verifyPageReadyForSubmission(page, email) {
        try {
            // Check if any loading indicators are present
            const loadingElements = await page.$$eval(
                '.loading, .spinner, [class*="loading"], [class*="spinner"]',
                elements => elements.filter(el => el.offsetParent !== null).length
            );

            if (loadingElements > 0) {
                logger.debug('Page still loading after CAPTCHA, waiting...', { email });
                await this.humanizer.delay(2000);
            }

            // Check if CAPTCHA elements are properly hidden/solved
            const captchaElements = await page.$$eval(
                '.h-captcha, .g-recaptcha, [class*="captcha"]',
                elements => elements.map(el => ({
                    visible: el.offsetParent !== null,
                    className: el.className
                }))
            );

            logger.debug('CAPTCHA elements state after solving', {
                email,
                captchaElements
            });

        } catch (error) {
            logger.debug('Page readiness verification failed', {
                email,
                error: error.message
            });
        }
    }

    /**
     * Submit the registration form
     */
    async submitForm(page, email) {
        logger.browserAction(email, 'submit_form');

        // Wait a moment after CAPTCHA solving for page to stabilize
        await this.humanizer.delay(2000);

        // First, let's debug what's on the page
        await this.debugPageState(page, email);

        const selectors = [
            // Primary Steam-specific selectors
            steamSelectors.continueButton,
            steamSelectors.continueButtonAlt,
            steamSelectors.createAccountButton,
            steamSelectors.submitButton,
            steamSelectors.steamSubmitButton,
            steamSelectors.steamContinueButton,
            steamSelectors.formSubmitButton,

            // Text-based selectors for different languages/variations
            'button:has-text("Continue")',
            'button:has-text("Create Account")',
            'button:has-text("Create My Account")',
            'button:has-text("Submit")',
            'button:has-text("Next")',
            'button:has-text("Proceed")',

            // ID and class-based selectors
            'button[id*="continue"]',
            'button[id*="submit"]',
            'button[id*="create"]',
            'button[class*="continue"]',
            'button[class*="submit"]',
            'button[class*="btn_green"]',
            'button[class*="btn_medium"]',

            // Form-based selectors
            'input[type="submit"]',
            'button[type="submit"]',
            'form button:last-child',
            'form input[type="submit"]:last-child',
            'form button:not([type="button"])',

            // Fallback selectors (most permissive)
            'button:not([disabled]):not([type="button"])',
            '[type="submit"]:not([disabled])',
            'button:not([disabled])'
        ];

        for (const selector of selectors) {
            try {
                // Check if element exists first
                const element = await page.$(selector);
                if (!element) {
                    logger.debug('Submit button selector not found', { selector });
                    continue;
                }

                // Wait for element to be interactable
                await this.humanizer.waitForInteractable(page, selector, 3000);

                // Check if button is visible and enabled
                const isVisible = await page.isVisible(selector);
                const isEnabled = await page.isEnabled(selector);
                const buttonText = await element.textContent();

                logger.debug('Submit button candidate found', {
                    selector,
                    visible: isVisible,
                    enabled: isEnabled,
                    text: buttonText?.trim()
                });

                if (!isVisible || !isEnabled) {
                    logger.debug('Submit button not ready', {
                        selector,
                        visible: isVisible,
                        enabled: isEnabled,
                        text: buttonText?.trim()
                    });
                    continue;
                }

                // Additional validation for Steam-specific buttons
                if (buttonText && (
                    buttonText.toLowerCase().includes('continue') ||
                    buttonText.toLowerCase().includes('create') ||
                    buttonText.toLowerCase().includes('submit') ||
                    buttonText.toLowerCase().includes('next')
                )) {
                    logger.info('Found valid submit button', {
                        email,
                        selector,
                        text: buttonText.trim()
                    });

                    try {
                        // Click submit button
                        logger.info('Attempting to click submit button', { email, selector });
                        await this.humanizer.humanClick(page, selector);
                        logger.info('Submit button clicked successfully', { email, selector });

                        // Wait for response
                        logger.info('Waiting for submission result', { email });
                        await this.waitForSubmissionResult(page, email);
                        logger.info('Submission result received', { email });

                        // Parse and return result
                        const result = await this.parseSubmissionResult(page);
                        logger.info('Form submission completed successfully', { email, result });
                        return result;

                    } catch (clickError) {
                        logger.warn('Failed to click or process submit button', {
                            email,
                            selector,
                            error: clickError.message
                        });

                        // If this is a critical error (not just a selector issue),
                        // we should try a different approach
                        if (clickError.message.includes('timeout') ||
                            clickError.message.includes('navigation') ||
                            clickError.message.includes('submission')) {

                            // Try alternative clicking method
                            logger.info('Trying alternative click method', { email, selector });
                            try {
                                await this.alternativeButtonClick(page, selector, email);
                                await this.waitForSubmissionResult(page, email);
                                const result = await this.parseSubmissionResult(page);
                                logger.info('Alternative click method succeeded', { email });
                                return result;
                            } catch (altError) {
                                logger.warn('Alternative click method also failed', {
                                    email,
                                    error: altError.message
                                });
                            }
                        }

                        // Continue to next selector
                        continue;
                    }
                }

            } catch (error) {
                logger.debug('Submit button selector failed', {
                    selector,
                    error: error.message
                });
                continue;
            }
        }

        // If we get here, no button was found - take a screenshot for debugging
        await this.captureDebugScreenshot(page, email, 'no-submit-button');

        throw new Error('Could not find or click submit button');
    }

    /**
     * Wait for form submission result
     */
    async waitForSubmissionResult(page, email) {
        logger.browserAction(email, 'waiting_for_response');

        const startUrl = page.url();
        logger.debug('Starting submission wait', { email, startUrl });

        try {
            // Wait for either success or error with more specific logging
            const result = await Promise.race([
                page.waitForSelector(steamSelectors.successMessage, { timeout: 15000 })
                    .then(() => ({ type: 'success', selector: steamSelectors.successMessage })),
                page.waitForSelector(steamSelectors.errorMessage, { timeout: 15000 })
                    .then(() => ({ type: 'error', selector: steamSelectors.errorMessage })),
                page.waitForSelector(steamSelectors.emailVerificationMessage, { timeout: 15000 })
                    .then(() => ({ type: 'verification', selector: steamSelectors.emailVerificationMessage })),
                page.waitForNavigation({ timeout: 15000 })
                    .then(() => ({ type: 'navigation', url: page.url() }))
            ]);

            logger.info('Submission result detected', { email, result });

        } catch (error) {
            // More detailed error handling
            const currentUrl = page.url();
            const urlChanged = currentUrl !== startUrl;

            logger.warn('Submission wait timeout or error', {
                email,
                error: error.message,
                startUrl,
                currentUrl,
                urlChanged
            });

            // Check if URL changed (might indicate success even without specific selectors)
            if (urlChanged) {
                logger.info('URL changed during submission, considering as potential success', {
                    email,
                    startUrl,
                    currentUrl
                });
                return; // Continue to parse result
            }

            // Check if we're still on the same page
            if (currentUrl.includes('join')) {
                // Take a screenshot for debugging
                await this.captureDebugScreenshot(page, email, 'submission-timeout');
                throw new Error(`Form submission timeout - still on registration page after 15s. URL: ${currentUrl}`);
            }

            // If URL changed but we don't recognize the pattern, continue anyway
            logger.info('URL changed to unknown page, attempting to parse result', {
                email,
                currentUrl
            });
        }
    }

    /**
     * Parse the submission result
     */
    async parseSubmissionResult(page) {
        const currentUrl = page.url();
        const pageTitle = await page.title();

        logger.debug('Parsing submission result', {
            currentUrl,
            pageTitle
        });

        // Check for success indicators
        const successElement = await page.$(steamSelectors.successMessage);
        const emailVerificationElement = await page.$(steamSelectors.emailVerificationMessage);

        // Additional success indicators based on URL patterns
        const urlSuccessIndicators = [
            'account/newaccountverification',
            'account/creation',
            'login',
            'store'
        ];

        const urlIndicatesSuccess = urlSuccessIndicators.some(indicator =>
            currentUrl.toLowerCase().includes(indicator)
        );

        // Check page content for success indicators
        const pageContent = await page.evaluate(() => {
            const bodyText = document.body.textContent.toLowerCase();
            return {
                hasVerificationText: bodyText.includes('verification') || bodyText.includes('verify'),
                hasSuccessText: bodyText.includes('success') || bodyText.includes('created'),
                hasEmailText: bodyText.includes('email sent') || bodyText.includes('check your email'),
                hasWelcomeText: bodyText.includes('welcome') || bodyText.includes('account created')
            };
        });

        if (successElement || emailVerificationElement || urlIndicatesSuccess ||
            pageContent.hasVerificationText || pageContent.hasSuccessText ||
            pageContent.hasEmailText || pageContent.hasWelcomeText) {

            const result = {
                success: true,
                message: 'Account registration completed successfully',
                requiresEmailVerification: !!(emailVerificationElement || pageContent.hasVerificationText || pageContent.hasEmailText),
                currentUrl: currentUrl,
                pageTitle: pageTitle,
                indicators: {
                    successElement: !!successElement,
                    emailVerificationElement: !!emailVerificationElement,
                    urlIndicatesSuccess,
                    pageContent
                }
            };

            logger.info('Registration success detected', result);
            return result;
        }

        // Check for error messages
        const errorElement = await page.$(steamSelectors.errorMessage);
        const fieldErrorElement = await page.$(steamSelectors.fieldError);

        if (errorElement || fieldErrorElement) {
            const errorText = errorElement ?
                await errorElement.textContent() :
                await fieldErrorElement.textContent();

            logger.error('Registration error detected', { errorText, currentUrl });
            throw new Error(`Registration failed: ${errorText}`);
        }

        // Check if we're still on the registration page (might indicate failure)
        if (currentUrl.includes('join')) {
            logger.warn('Still on registration page, unclear result', { currentUrl, pageTitle });

            // Take a screenshot for debugging
            await this.captureDebugScreenshot(page, 'unknown-email', 'unclear-result');

            throw new Error(`Registration result unclear - still on join page. URL: ${currentUrl}`);
        }

        // If we're on a different page but no clear success indicators
        logger.warn('Unknown registration result', { currentUrl, pageTitle });
        throw new Error(`Unknown registration result. URL: ${currentUrl}, Title: ${pageTitle}`);
    }

    /**
     * Get random country for testing
     */
    getRandomCountry() {
        return steamConfig.testCountries[
            Math.floor(Math.random() * steamConfig.testCountries.length)
        ];
    }

    /**
     * Check if error is proxy-related
     */
    isProxyError(error) {
        const proxyErrorKeywords = [
            'proxy',
            'econnrefused',
            'etimedout',
            'network',
            'connection refused',
            'connection timeout',
            'tunnel_connection_failed',
            'err_tunnel_connection_failed',
            'net::err_tunnel_connection_failed',
            'tunnel connection failed'
        ];

        const errorMessage = error.message.toLowerCase();
        return proxyErrorKeywords.some(keyword => errorMessage.includes(keyword));
    }

    /**
     * Cleanup resources for an email
     */
    async cleanup(email) {
        try {
            await this.browser.closeContext(email);
            this.proxyManager.releaseProxy(email);
        } catch (error) {
            logger.warn('Cleanup error', { email, error: error.message });
        }
    }

    /**
     * Try BrightData Web Unlocker as a fallback when browser automation fails
     */
    async tryBrightDataFallback(email, password, country) {
        try {
            logger.info('Attempting Steam registration via BrightData Web Unlocker', { email });

            // Use BrightData to fetch the registration page with CAPTCHA solving
            const result = await this.captchaSolver.fetchPageWithBrightData(
                config.steam.registrationUrl,
                email
            );

            if (result.success) {
                // Parse the HTML content to check if registration was successful
                const htmlContent = result.content;

                // Look for success indicators in the HTML
                if (htmlContent.includes('account created') ||
                    htmlContent.includes('registration successful') ||
                    htmlContent.includes('welcome') ||
                    htmlContent.includes('verify your email')) {

                    logger.accountSuccess(email, {
                        method: 'brightdata-web-unlocker',
                        message: 'Registration completed via BrightData Web Unlocker'
                    });

                    return {
                        success: true,
                        method: 'brightdata-web-unlocker',
                        message: 'Account registration completed via BrightData Web Unlocker'
                    };
                }

                // If we got the page but no success indicators,
                // it might need manual form submission
                logger.warn('BrightData fetched page but registration status unclear', {
                    email,
                    contentPreview: htmlContent.substring(0, 200) + '...'
                });

                return {
                    success: false,
                    method: 'brightdata-web-unlocker',
                    message: 'Page fetched but registration status unclear',
                    content: htmlContent
                };
            }

            throw new Error('BrightData Web Unlocker failed to fetch page');

        } catch (error) {
            logger.error('BrightData fallback failed', { email, error: error.message });
            throw error;
        }
    }

    /**
     * Alternative button clicking method using JavaScript execution
     */
    async alternativeButtonClick(page, selector, email) {
        logger.info('Using alternative button click method', { email, selector });

        try {
            // Method 1: Direct JavaScript click
            await page.evaluate((sel) => {
                const element = document.querySelector(sel);
                if (element) {
                    element.click();
                    return true;
                }
                return false;
            }, selector);

            logger.info('Alternative click method 1 (JS click) executed', { email });
            await this.humanizer.delay(1000);

        } catch (error) {
            logger.debug('JS click failed, trying form submission', { email, error: error.message });

            // Method 2: Form submission
            try {
                await page.evaluate(() => {
                    const forms = document.querySelectorAll('form');
                    if (forms.length > 0) {
                        forms[0].submit();
                        return true;
                    }
                    return false;
                });

                logger.info('Alternative click method 2 (form submit) executed', { email });
                await this.humanizer.delay(1000);

            } catch (formError) {
                logger.debug('Form submit failed, trying Enter key', { email, error: formError.message });

                // Method 3: Press Enter key
                await page.keyboard.press('Enter');
                logger.info('Alternative click method 3 (Enter key) executed', { email });
                await this.humanizer.delay(1000);
            }
        }
    }

    /**
     * Debug page state for troubleshooting
     */
    async debugPageState(page, email) {
        try {
            const url = page.url();
            const title = await page.title();

            // Count form elements
            const formElements = await page.evaluate(() => {
                return {
                    forms: document.querySelectorAll('form').length,
                    buttons: document.querySelectorAll('button').length,
                    submitInputs: document.querySelectorAll('input[type="submit"]').length,
                    inputs: document.querySelectorAll('input').length,
                    selects: document.querySelectorAll('select').length,
                    captchas: document.querySelectorAll('.h-captcha, .g-recaptcha').length
                };
            });

            logger.debug('Page state debug', {
                email,
                url,
                title,
                elements: formElements
            });

            // List all buttons with their text
            const buttons = await page.evaluate(() => {
                const buttonElements = Array.from(document.querySelectorAll('button, input[type="submit"]'));
                return buttonElements.map((btn, index) => ({
                    index,
                    tagName: btn.tagName,
                    type: btn.type,
                    text: btn.textContent?.trim() || btn.value || '',
                    id: btn.id,
                    className: btn.className,
                    disabled: btn.disabled,
                    visible: btn.offsetParent !== null
                }));
            });

            logger.debug('Available buttons', { email, buttons });

        } catch (error) {
            logger.warn('Debug page state failed', { email, error: error.message });
        }
    }

    /**
     * Capture screenshot for debugging
     */
    async captureDebugScreenshot(page, email, reason) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `debug-${reason}-${email.replace(/[^a-zA-Z0-9]/g, '_')}-${timestamp}.png`;
            const filepath = `./logs/${filename}`;

            await page.screenshot({
                path: filepath,
                fullPage: true
            });

            logger.info('Debug screenshot captured', {
                email,
                reason,
                filepath
            });

        } catch (error) {
            logger.warn('Failed to capture debug screenshot', {
                email,
                error: error.message
            });
        }
    }

    /**
     * Get registration statistics
     */
    getStats() {
        return {
            browserStats: this.browser.getStats(),
            proxyStats: this.proxyManager.getStats()
        };
    }
}

module.exports = SteamRegistrar;