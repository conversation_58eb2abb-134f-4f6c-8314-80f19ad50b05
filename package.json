{"name": "steam-account-creator", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "DEBUG=true HEADLESS=false node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["steam", "automation", "playwright", "proxy", "<PERSON><PERSON>a"], "author": "", "license": "ISC", "description": "Automated Steam account creation tool with proxy rotation and captcha solving", "engines": {"node": ">=16.0.0"}, "dependencies": {"axios": "^1.10.0", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "playwright": "^1.53.1", "winston": "^3.17.0"}}